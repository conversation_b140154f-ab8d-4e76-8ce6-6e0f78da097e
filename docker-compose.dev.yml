# Development override for docker-compose.yml
# Use with: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

version: '3.8'

services:
  app:
    # Override environment for development
    environment:
      NODE_ENV: development
      SERVER_PORT: 3011
      DB_HOST: database
      DB_USER: ${DB_USER:-appuser}
      DB_PASSWORD: ${DB_PASSWORD:-apppassword123}
      DB_NAME: ${DB_NAME:-video_markers}
    # Development volumes - map source code
    volumes:
      - ./:/app                    # Map entire project directory
      - /app/node_modules          # Prevent overwriting node_modules
      - ./logs:/app/logs           # Map logs directory
    # Use node with --watch for auto-restart on file changes (Node 18+)
    command: node --watch server.js
    # Expose additional ports for debugging if needed
    ports:
      - "${APP_PORT:-3011}:3011"
      - "9229:9229"  # Node.js debug port
    
  database:
    # Expose database port for external access in development
    ports:
      - "3306:3306"
    # Add development-specific environment variables
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-rootpassword123}
      MYSQL_DATABASE: ${DB_NAME:-video_markers}
      MYSQL_USER: ${DB_USER:-appuser}
      MYSQL_PASSWORD: ${DB_PASSWORD:-apppassword123}
      # Enable general query log for debugging
      MYSQL_GENERAL_LOG: 1
      MYSQL_GENERAL_LOG_FILE: /var/log/mysql/general.log
    # Map database logs
    volumes:
      - db_data:/var/lib/mysql
      - ./init-db:/docker-entrypoint-initdb.d
      - ./logs/mysql:/var/log/mysql

# Development-specific volumes
volumes:
  db_data:
    driver: local
