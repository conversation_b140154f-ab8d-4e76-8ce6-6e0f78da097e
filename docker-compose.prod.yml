# Production override for docker-compose.yml
# Use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

version: '3.8'

services:
  database:
    # Production database optimizations
    command: >
      --innodb-buffer-pool-size=256M
      --innodb-log-file-size=64M
      --innodb-flush-log-at-trx-commit=2
      --innodb-file-per-table=1
      --max-connections=200
      --query-cache-size=32M
      --query-cache-type=1
    # Remove port exposure for security
    ports: []
    
  app:
    # Production environment variables
    environment:
      NODE_ENV: production
      SERVER_PORT: 3011
      DB_HOST: database
      DB_USER: ${DB_USER:-appuser}
      DB_PASSWORD: ${DB_PASSWORD:-apppassword123}
      DB_NAME: ${DB_NAME:-video_markers}
      # Additional production settings
      NODE_OPTIONS: "--max-old-space-size=512"
    # Remove direct port exposure (use nginx)
    ports: []
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    # Restart policy
    restart: always
    
  nginx:
    # Always include nginx in production
    profiles: []
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'
        reservations:
          memory: 64M
          cpus: '0.1'
    # Additional nginx configuration for production
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    # Restart policy
    restart: always

# Additional production volumes
volumes:
  logs:
    driver: local
