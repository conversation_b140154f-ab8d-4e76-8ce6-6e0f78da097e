{"name": "video-markers-app", "version": "1.0.0", "description": "Video time markers application with MariaDB backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "keywords": ["video", "markers", "time", "tracking"], "author": "", "license": "ISC", "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "mariadb": "^3.2.0"}, "devDependencies": {"nodemon": "^3.0.1"}}