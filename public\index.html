<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Marcadores de tiempo para videos</title>
  <style>
    :root{
      --bg:#0f172a;/*slate-900*/
      --panel:#111827;/*gray-900*/
      --soft:#1f2937;/*gray-800*/
      --text:#e5e7eb;/*gray-200*/
      --muted:#9ca3af;/*gray-400*/
      --acc:#22d3ee;/*cyan-400*/
      --ok:#22c55e;--warn:#f59e0b;--err:#ef4444;
      --radius:16px;
    }
    *{box-sizing:border-box}
    body{margin:0;background:linear-gradient(180deg,var(--bg),#030712);font-family:system-ui,-apple-system,Segoe UI,Roboto,Ubuntu,Inter,Arial;color:var(--text)}
    .wrap{max-width:980px;margin:28px auto;padding:16px}
    header{display:flex;gap:16px;align-items:center;justify-content:space-between;margin-bottom:18px}
    h1{font-size:clamp(18px,3vw,28px);margin:0;letter-spacing:.2px}
    .card{background:linear-gradient(180deg,var(--panel),#0b1020);border:1px solid #1f2937;border-radius:var(--radius);box-shadow:0 10px 30px rgba(0,0,0,.35)}
    form{display:grid;grid-template-columns:1fr 1fr 160px;gap:12px;padding:16px}
    label{font-size:12px;color:var(--muted)}
    .field{display:flex;flex-direction:column;gap:6px}
    input, textarea{background:var(--soft);border:1px solid #263042;color:var(--text);border-radius:12px;padding:10px 12px;font-size:14px;outline:none}
    input:focus, textarea:focus{border-color:var(--acc);box-shadow:0 0 0 3px rgba(34,211,238,.15)}
    .actions{display:flex;gap:8px;align-items:center;justify-content:flex-end}
    button{border:0;border-radius:999px;padding:10px 14px;font-weight:600;cursor:pointer}
    .primary{background:var(--acc);color:#0b1020}
    .ghost{background:transparent;border:1px solid #263042;color:var(--text)}
    .list{margin-top:16px}
    .item{display:grid;grid-template-columns:1fr 220px 140px 1fr auto;align-items:center;gap:12px;padding:14px;border-top:1px solid #1f2937}
    .item:nth-child(odd){background:rgba(255,255,255,.02)}
    .mono{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace}
    .badge{display:inline-block;padding:4px 10px;border-radius:999px;background:#0a1425;border:1px solid #223455;color:#c7e8ff;font-weight:600}
    .rowhead{display:grid;grid-template-columns:1fr 220px 140px 1fr auto;gap:12px;padding:10px 14px;color:var(--muted);font-size:12px}
    .empty{padding:22px;text-align:center;color:var(--muted)}
    .small{font-size:12px;color:var(--muted)}
    .tools{display:flex;gap:8px}
    .tools button{padding:8px 10px}
    .danger{background:transparent;border:1px solid #3a2330;color:#ffb4b4}
    .ok{background:transparent;border:1px solid #1f3a2a;color:#b8f7c3}
    .warn{background:transparent;border:1px solid #3a341f;color:#ffe1a8}
    .footer{display:flex;gap:8px;flex-wrap:wrap;align-items:center;justify-content:space-between;padding:12px 16px;border-top:1px solid #1f2937}
    .hint{font-size:12px;color:var(--muted)}
    @media (max-width:900px){
      form{grid-template-columns:1fr}
      .rowhead,.item{grid-template-columns:1fr}
    }
  </style>
</head>
<body>
  <div class="wrap">
    <header>
      <h1>📍 Marcadores de tiempo para videos (YouTube o cualquier enlace)</h1>
      <div class="tools">
        <button class="ghost" id="btnExport">Exportar JSON</button>
        <button class="ghost" id="btnImport">Importar JSON</button>
        <input type="file" id="fileImport" accept="application/json" hidden>
        <button class="ghost" id="btnClear">Vaciar lista</button>
      </div>
    </header>

    <section class="card">
      <form id="form">
        <div class="field">
          <label for="title">Titulo (opcional)</label>
          <input id="title" placeholder="Ej.: Curso Revit – Clase 3" />
        </div>
        <div class="field">
          <label for="url">URL del video</label>
          <input id="url" placeholder="Pega aqui el enlace (YouTube, Drive, etc.)" required />
        </div>
        <div class="field">
          <label for="time">Minuto (hh:mm:ss o mm:ss o segundos)</label>
          <input id="time" class="mono" placeholder="Ej.: 29:34" required />
        </div>
        <div class="field" style="grid-column:1/-1">
          <label for="note">Nota (opcional)</label>
          <textarea id="note" rows="2" placeholder="Que estaba viendo o por que paro"></textarea>
        </div>
        <div class="actions" style="grid-column:1/-1">
          <span id="feedback" class="hint"></span>
          <button type="button" class="ghost" id="btnPreview">Previsualizar enlace</button>
          <button type="submit" class="primary">Guardar</button>
        </div>
      </form>

      <div class="rowhead">
        <div>Titulo / URL</div>
        <div>Tiempo</div>
        <div>Fecha</div>
        <div>Nota</div>
        <div>Acciones</div>
      </div>
      <div id="list" class="list"></div>
      <div class="footer">
        <div class="hint">Los marcadores se guardan en el servidor. Usa Exportar/Importar para moverlos a otro equipo.</div>
        <div class="hint">Consejo: para YouTube se abre justo en ese segundo. Para otros enlaces se abre la URL original.</div>
      </div>
    </section>
  </div>

  <script src="/app.js"></script>
</body>
</html>
